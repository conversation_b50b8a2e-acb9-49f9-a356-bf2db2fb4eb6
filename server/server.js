require('dotenv').config({ path: '../.env' });
const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const { S3Client, PutObjectCommand, GetObjectCommand, ListObjectsV2Command, HeadObjectCommand } = require('@aws-sdk/client-s3');
const { updateMetadataCSV } = require('./utils/metadataManager');
const { logMetricsByDemographic, getMetricsByDemographic } = require('./utils/sageMakerManager');

const app = express();
// Use environment variable for port, fallback to 5000
const port = process.env.PORT || 5000;

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({ storage });

// CORS configuration
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3003',
    'http://localhost:3004',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:3001'
  ],
  credentials: true,
  optionsSuccessStatus: 200
};

// Enable CORS for all routes
app.use(cors(corsOptions));

// Parse JSON bodies with error handling
app.use(express.json({ limit: process.env.MAX_FILE_SIZE || '50mb' }));
app.use(express.urlencoded({ extended: true, limit: process.env.MAX_FILE_SIZE || '50mb' }));

// Request logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);

  if (err.type === 'entity.too.large') {
    return res.status(413).json({
      error: 'File too large',
      message: 'The uploaded file exceeds the maximum allowed size'
    });
  }

  if (err.type === 'entity.parse.failed') {
    return res.status(400).json({
      error: 'Invalid JSON',
      message: 'The request body contains invalid JSON'
    });
  }

  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// Root endpoint for browser access
app.get('/', (req, res) => {
  res.json({
    message: 'ICU Dataset Application - Backend Server',
    version: '1.0.0',
    status: 'operational',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      upload: '/upload',
      systemInfo: '/api/system-info',
      sampleCounts: '/api/sample-counts',
      metrics: '/api/metrics'
    },
    documentation: 'This is the backend API server for the ICU Dataset Application. Use the endpoints above to interact with the service.'
  });
});

// Enhanced health check endpoint
app.get('/health', async (req, res) => {
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0',
    services: {
      server: 'operational',
      aws: 'unknown',
      storage: 'operational'
    },
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      external: Math.round(process.memoryUsage().external / 1024 / 1024)
    }
  };

  // Check AWS connectivity if credentials are available
  try {
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
      // Simple AWS connectivity test could be added here
      healthCheck.services.aws = 'configured';
    } else {
      healthCheck.services.aws = 'not_configured';
    }
  } catch (error) {
    healthCheck.services.aws = 'error';
    healthCheck.status = 'degraded';
  }

  // Set overall status
  const hasErrors = Object.values(healthCheck.services).some(status => status === 'error');
  if (hasErrors) {
    healthCheck.status = 'unhealthy';
    res.status(503);
  } else if (Object.values(healthCheck.services).some(status => status === 'degraded')) {
    healthCheck.status = 'degraded';
  }

  res.json(healthCheck);
});

// Test receipt mapping S3 connectivity
app.post('/test-receipt-mapping', async (req, res) => {
  try {
    console.log('🧪 Testing receipt mapping S3 connectivity...');

    // Test S3 connectivity by listing objects in receipt-numbers folder
    const listCommand = new ListObjectsV2Command({
      Bucket: process.env.S3_BUCKET_NAME,
      Prefix: 'receipt-numbers/',
      MaxKeys: 10
    });

    const listResponse = await s3Client.send(listCommand);

    // Test creating a simple test file
    const testKey = 'receipt-numbers/test-connectivity.json';
    const testData = {
      test: true,
      timestamp: new Date().toISOString(),
      message: 'Receipt mapping S3 connectivity test'
    };

    const putCommand = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET_NAME,
      Key: testKey,
      Body: JSON.stringify(testData, null, 2),
      ContentType: 'application/json'
    });

    await s3Client.send(putCommand);

    res.json({
      success: true,
      message: 'Receipt mapping S3 connectivity test passed',
      existingFiles: listResponse.Contents ? listResponse.Contents.length : 0,
      testFileCreated: testKey,
      bucket: process.env.S3_BUCKET_NAME
    });

  } catch (error) {
    console.error('❌ Receipt mapping S3 test failed:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Receipt mapping S3 connectivity test failed'
    });
  }
});

// System info endpoint for debugging
app.get('/api/system-info', (req, res) => {
  const systemInfo = {
    node_version: process.version,
    platform: process.platform,
    architecture: process.arch,
    uptime: process.uptime(),
    memory_usage: process.memoryUsage(),
    cpu_usage: process.cpuUsage(),
    environment: process.env.NODE_ENV || 'development',
    port: port,
    aws_configured: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY),
    s3_bucket: process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting',
    timestamp: new Date().toISOString()
  };

  res.json(systemInfo);
});

// S3 connectivity test endpoint
app.get('/api/test-s3', async (req, res) => {
  console.log('=== S3 CONNECTIVITY TEST ===');

  try {
    // Check if AWS credentials are configured
    if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
      return res.json({
        success: false,
        error: 'AWS credentials not configured',
        details: 'AWS_ACCESS_KEY_ID or AWS_SECRET_ACCESS_KEY missing'
      });
    }

    // Test S3 connection by listing bucket contents
    const { S3Client, ListObjectsV2Command } = require('@aws-sdk/client-s3');

    const s3Client = new S3Client({
      region: process.env.AWS_REGION || 'ap-southeast-2',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      }
    });

    const bucketName = process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting';

    console.log(`Testing S3 connection to bucket: ${bucketName}`);

    const command = new ListObjectsV2Command({
      Bucket: bucketName,
      MaxKeys: 5
    });

    const result = await s3Client.send(command);

    console.log('✅ S3 connection successful');

    res.json({
      success: true,
      bucket: bucketName,
      region: process.env.AWS_REGION || 'ap-southeast-2',
      objectCount: result.KeyCount || 0,
      sampleFiles: result.Contents?.slice(0, 3).map(obj => ({
        key: obj.Key,
        size: obj.Size,
        lastModified: obj.LastModified
      })) || []
    });

  } catch (error) {
    console.error('❌ S3 connection failed:', error);

    res.json({
      success: false,
      error: error.message,
      errorCode: error.name,
      statusCode: error.$metadata?.httpStatusCode,
      details: {
        bucket: process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting',
        region: process.env.AWS_REGION || 'ap-southeast-2',
        hasCredentials: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY)
      }
    });
  }
});

// Error logging endpoint
app.post('/api/log-error', express.json(), (req, res) => {
  const { error, context, severity, timestamp } = req.body;

  const logEntry = {
    timestamp: timestamp || new Date().toISOString(),
    context: context || 'Unknown',
    severity: severity || 'medium',
    error: error || 'Unknown error',
    ip: req.ip,
    userAgent: req.get('User-Agent')
  };

  // Log to console with appropriate level
  switch (severity) {
    case 'critical':
    case 'high':
      console.error('🚨 CLIENT ERROR:', logEntry);
      break;
    case 'medium':
      console.warn('⚠️ CLIENT WARNING:', logEntry);
      break;
    case 'low':
      console.info('ℹ️ CLIENT INFO:', logEntry);
      break;
    default:
      console.log('📝 CLIENT LOG:', logEntry);
  }

  res.json({ success: true, logged: true });
});

// Upload endpoint
app.post('/upload', upload.single('video'), async (req, res) => {
  console.log('=== SERVER UPLOAD: Request received ===');
  console.log('📥 Request details:', {
    hasFile: !!req.file,
    fileSize: req.file?.size,
    fileName: req.file?.originalname,
    mimeType: req.file?.mimetype,
    bodyKeys: Object.keys(req.body)
  });

  try {
    if (!req.file) {
      console.error('❌ No video file provided in request');
      return res.status(400).json({
        error: 'No video file provided'
      });
    }

    const { buffer } = req.file;
    const {
      category,
      phrase,
      recordingNumber,
      demographics
    } = req.body;

    console.log('📋 Request metadata:', {
      category,
      phrase,
      recordingNumber,
      demographicsType: typeof demographics,
      demographicsLength: demographics?.length
    });

    if (!category || !phrase || !recordingNumber || !demographics) {
      console.error('❌ Missing required metadata:', {
        hasCategory: !!category,
        hasPhrase: !!phrase,
        hasRecordingNumber: !!recordingNumber,
        hasDemographics: !!demographics
      });
      return res.status(400).json({
        error: 'Missing required metadata'
      });
    }

    // Parse demographics JSON if it's a string
    let parsedDemographics;
    try {
      parsedDemographics = typeof demographics === 'string'
        ? JSON.parse(demographics)
        : demographics;
      console.log('✅ Demographics parsed successfully:', parsedDemographics);
    } catch (parseError) {
      console.error('❌ Failed to parse demographics JSON:', parseError);
      return res.status(400).json({
        error: 'Invalid demographics JSON format'
      });
    }

    // Validate demographics
    const requiredFields = ['userId', 'ageGroup', 'gender', 'ethnicity'];
    const missingFields = requiredFields.filter(field => !parsedDemographics[field]);

    if (missingFields.length > 0) {
      console.error('❌ Missing required demographic fields:', missingFields);
      return res.status(400).json({
        error: `Invalid demographics data - missing fields: ${missingFields.join(', ')}`
      });
    }

    console.log('✅ All validation passed');

    // Check if we have AWS credentials
    const hasAwsCredentials = !!process.env.AWS_ACCESS_KEY_ID && !!process.env.AWS_SECRET_ACCESS_KEY;
    console.log('🔑 AWS credentials check:', {
      hasAccessKey: !!process.env.AWS_ACCESS_KEY_ID,
      hasSecretKey: !!process.env.AWS_SECRET_ACCESS_KEY,
      hasAwsCredentials,
      awsRegion: process.env.AWS_REGION,
      s3Bucket: process.env.AWS_S3_BUCKET
    });

    if (hasAwsCredentials) {
      // Upload to AWS S3 if credentials exist
      console.log('🚀 Using real AWS S3 upload with credentials');
      try {
        const result = await uploadToS3(
          buffer,
          category,
          phrase,
          recordingNumber,
          parsedDemographics
        );
        console.log('✅ S3 upload completed successfully:', {
          success: result.success,
          filePath: result.filePath,
          url: result.url
        });
        res.json(result);
      } catch (s3Error) {
        console.error('❌ S3 upload failed:', s3Error);
        throw s3Error;
      }
    } else {
      // Use mock upload if no credentials
      console.log('🔄 Using mock upload (AWS credentials missing)');
      try {
        const result = await mockUpload(
          buffer,
          category,
          phrase,
          recordingNumber,
          parsedDemographics
        );
        console.log('✅ Mock upload completed successfully:', {
          success: result.success,
          filePath: result.filePath,
          message: result.message
        });
        res.json(result);
      } catch (mockError) {
        console.error('❌ Mock upload failed:', mockError);
        throw mockError;
      }
    }
  } catch (error) {
    console.error('=== SERVER UPLOAD ERROR ===');
    console.error('Error details:', error);
    console.error('Error stack:', error.stack);

    // Provide more specific error responses
    let statusCode = 500;
    let errorMessage = error.message || 'Internal server error';

    if (error.message?.includes('AWS_CREDENTIALS_ERROR')) {
      statusCode = 401;
      errorMessage = 'AWS credentials not configured properly';
    } else if (error.message?.includes('AWS_ACCESS_DENIED')) {
      statusCode = 403;
      errorMessage = 'Access denied to S3 bucket';
    } else if (error.message?.includes('AWS_BUCKET_NOT_FOUND')) {
      statusCode = 404;
      errorMessage = 'S3 bucket not found';
    } else if (error.message?.includes('AWS_NETWORK_ERROR')) {
      statusCode = 503;
      errorMessage = 'Network error during S3 upload';
    }

    res.status(statusCode).json({
      error: errorMessage,
      timestamp: new Date().toISOString()
    });
  }
});

// Mock upload function for testing when AWS credentials are missing
async function mockUpload(buffer, category, phrase, recordingNumber, demographics) {
  console.log('MOCK UPLOAD: Using mock function since AWS credentials are not configured');
  
  // Simulate some processing time
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Format timestamp in required format: YYYYMMDDTHHMMSS
  const now = new Date();
  const timestamp = now.toISOString()
    .replace(/[-:]/g, '')
    .replace(/\..+/, '');
  
  // Sanitize phrase to match naming convention (lowercase, underscores only, no punctuation)
  const sanitizedPhrase = phrase
    .toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove punctuation
    .replace(/\s+/g, '_'); // Replace spaces with underscores
  
  // Create filename with required format
  const filename = [
    sanitizedPhrase,
    `user${demographics.userId.toString().padStart(2, '0')}`,
    demographics.ageGroup,
    demographics.gender,
    demographics.ethnicity,
    timestamp
  ].join('__') + '.webm';
  
  // Create folder structure matching AWS implementation
  const mockPath = `mock-storage/icu-videos/${demographics.ageGroup}/${demographics.gender}/${demographics.ethnicity}/${sanitizedPhrase}/${filename}`;
  
  console.log('MOCK UPLOAD: Generated mock path:', mockPath);
  
  // Return a mock response that matches the structure of the real S3 response
  return {
    success: true,
    blobName: filename,
    filePath: mockPath,
    url: `https://mock-storage/${mockPath}`,
    size: buffer.length,
    message: 'This is a mock upload. AWS credentials are not configured on the server.'
  };
}

// AWS S3 upload function
async function uploadToS3(buffer, category, phrase, recordingNumber, demographics) {
  console.log('=== S3 UPLOAD: Starting S3 upload process ===');

  try {
    // Get bucket name from environment variable - use specified S3 bucket
    const bucketName = process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting';
    const region = process.env.AWS_REGION || 'ap-southeast-2';

    console.log('🔧 S3 Configuration:', {
      bucketName,
      region,
      bufferSize: buffer.length,
      category,
      phrase,
      recordingNumber,
      demographics
    });

    // Format timestamp in required format: YYYYMMDDTHHMMSS
    const now = new Date();
    const timestamp = now.toISOString()
      .replace(/[-:]/g, '')
      .replace(/\..+/, '');

    console.log('⏰ Generated timestamp:', timestamp);

    // Sanitize phrase to match naming convention (lowercase, underscores only, no punctuation)
    const sanitizedPhrase = phrase
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .replace(/\s+/g, '_'); // Replace spaces with underscores

    console.log('🧹 Sanitized phrase:', sanitizedPhrase);
    
    // Create filename with required format:
    // [PHRASE_LABEL]__[USER_ID]__[AGEGROUP]__[GENDER]__[ETHNICITY]__[TIMESTAMP].mp4
    const filename = [
      sanitizedPhrase,
      `user${demographics.userId.toString().padStart(2, '0')}`,
      demographics.ageGroup,
      demographics.gender,
      demographics.ethnicity,
      timestamp
    ].join('__') + '.webm';
    
    // Create folder structure:
    // icu-videos/[AGEGROUP]/[GENDER]/[ETHNICITY]/[PHRASE_LABEL]/[FILENAME].mp4
    const key = `icu-videos/${demographics.ageGroup}/${demographics.gender}/${demographics.ethnicity}/${sanitizedPhrase}/${filename}`;

    console.log('📁 Generated S3 key:', key);

    // Generate metadata for the object
    const metadata = {
      phrase_label: sanitizedPhrase,
      user_id: `user${demographics.userId.toString().padStart(2, '0')}`,
      agegroup: demographics.ageGroup,
      gender: demographics.gender,
      ethnicity: demographics.ethnicity,
      timestamp,
      recording_number: recordingNumber.toString(),
      category
    };

    console.log('📋 Generated metadata:', metadata);

    // Create an S3 client
    console.log('🔧 Creating S3 client...');
    const s3Client = new S3Client({
      region,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      }
    });

    // Upload data to S3
    const uploadParams = {
      Bucket: bucketName,
      Key: key,
      Body: buffer,
      ContentType: 'video/webm',
      Metadata: metadata
    };

    console.log('📤 Upload parameters prepared:', {
      bucket: uploadParams.Bucket,
      key: uploadParams.Key,
      contentType: uploadParams.ContentType,
      bodySize: uploadParams.Body.length,
      metadataKeys: Object.keys(uploadParams.Metadata)
    });

    console.log('🚀 Sending upload to S3...');
    const uploadResponse = await s3Client.send(new PutObjectCommand(uploadParams));

    console.log('✅ S3 upload successful:', {
      etag: uploadResponse.ETag,
      location: uploadResponse.Location,
      key: key
    });
    
    // Update metadata.csv file with the recording information
    console.log('📝 Updating metadata CSV...');
    try {
      const metadataResult = await updateMetadataCSV(metadata, filename, key);
      console.log('✅ Metadata CSV update successful:', metadataResult);
    } catch (metadataError) {
      console.error('⚠️ Failed to update metadata.csv (continuing anyway):', metadataError);
      // Continue with the upload response even if metadata update fails
    }

    const result = {
      success: true,
      blobName: filename,
      filePath: key,
      url: `https://${bucketName}.s3.${region}.amazonaws.com/${key}`,
      response: uploadResponse,
      timestamp: new Date().toISOString()
    };

    console.log('🎉 S3 upload process completed successfully:', {
      success: result.success,
      filePath: result.filePath,
      url: result.url
    });

    return result;
  } catch (error) {
    console.error('=== S3 UPLOAD ERROR ===');
    console.error('Error details:', error);
    console.error('Error code:', error.code);
    console.error('Error name:', error.name);
    console.error('Error stack:', error.stack);

    // Categorize AWS errors for better debugging
    let errorType = 'AWS_UNKNOWN_ERROR';
    if (error.code === 'NoSuchBucket') {
      errorType = 'AWS_BUCKET_NOT_FOUND';
    } else if (error.code === 'AccessDenied') {
      errorType = 'AWS_ACCESS_DENIED';
    } else if (error.code === 'InvalidAccessKeyId') {
      errorType = 'AWS_CREDENTIALS_ERROR';
    } else if (error.code === 'SignatureDoesNotMatch') {
      errorType = 'AWS_CREDENTIALS_ERROR';
    } else if (error.name === 'NetworkError' || error.message?.includes('network')) {
      errorType = 'AWS_NETWORK_ERROR';
    } else if (error.message?.includes('too large')) {
      errorType = 'AWS_FILE_TOO_LARGE';
    }

    const errorResult = {
      success: false,
      error: `${errorType}: ${error.message}`,
      errorCode: error.code,
      errorName: error.name,
      timestamp: new Date().toISOString()
    };

    console.error('❌ S3 upload failed with categorized error:', errorResult);
    throw new Error(errorResult.error);
  }
}

// SageMaker metrics endpoints
// GET metrics by demographic
app.get('/api/metrics', async (req, res) => {
  try {
    // Extract optional filters from query params
    const filters = {};
    
    if (req.query.ageGroup) filters.ageGroup = req.query.ageGroup;
    if (req.query.gender) filters.gender = req.query.gender;
    if (req.query.ethnicity) filters.ethnicity = req.query.ethnicity;
    
    // Get metrics with optional filters
    const metricsResult = await getMetricsByDemographic(filters);
    
    res.json(metricsResult);
  } catch (error) {
    console.error('Error retrieving metrics:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
});

// POST new metrics data
app.post('/api/metrics', express.json(), async (req, res) => {
  try {
    const { metrics, demographics } = req.body;
    
    if (!metrics || !demographics) {
      return res.status(400).json({
        success: false,
        error: 'Missing required data: metrics and demographics are required'
      });
    }
    
    // Log metrics by demographic
    const result = await logMetricsByDemographic(metrics, demographics);
    
    res.json(result);
  } catch (error) {
    console.error('Error logging metrics:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
});

// Sample count tracking endpoint
app.get('/api/sample-counts', async (req, res) => {
  try {
    const bucketName = process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting';
    const region = process.env.AWS_REGION || 'us-east-1';
    const metadataKey = 'metadata.csv';
    
    // Create S3 client
    const s3Client = new S3Client({ region });
    
    try {
      // Get metadata file
      const getParams = {
        Bucket: bucketName,
        Key: metadataKey
      };
      
      const response = await s3Client.send(new GetObjectCommand(getParams));
      
      // Convert response body (stream) to string
      const chunks = [];
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      const csvContent = Buffer.concat(chunks).toString('utf-8');
      
      // Parse CSV
      const lines = csvContent.trim().split('\n');
      const headers = lines[0].split(',');
      
      // Count recordings per phrase, gender, age group, and ethnicity
      const sampleCounts = {
        byPhrase: {},
        byGender: { male: 0, female: 0, nonbinary: 0 },
        byAgeGroup: { '18to39': 0, '40to64': 0, '65plus': 0 },
        byEthnicity: {},
        total: lines.length - 1 // Exclude header row
      };
      
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',');
        const row = {};
        
        headers.forEach((header, index) => {
          row[header] = values[index];
        });
        
        // Count by phrase
        if (!sampleCounts.byPhrase[row.phrase_label]) {
          sampleCounts.byPhrase[row.phrase_label] = 0;
        }
        sampleCounts.byPhrase[row.phrase_label]++;
        
        // Count by demographics
        sampleCounts.byGender[row.gender]++;
        sampleCounts.byAgeGroup[row.agegroup]++;
        
        if (!sampleCounts.byEthnicity[row.ethnicity]) {
          sampleCounts.byEthnicity[row.ethnicity] = 0;
        }
        sampleCounts.byEthnicity[row.ethnicity]++;
      }
      
      // Return the counts
      res.json({
        success: true,
        counts: sampleCounts,
        lastUpdated: new Date().toISOString()
      });
      
    } catch (error) {
      if (error.name === 'NotFound' || error.name === 'NoSuchKey') {
        // No data yet
        res.json({
          success: true,
          counts: {
            byPhrase: {},
            byGender: { male: 0, female: 0, nonbinary: 0 },
            byAgeGroup: { '18to39': 0, '40to64': 0, '65plus': 0 },
            byEthnicity: {},
            total: 0
          },
          lastUpdated: new Date().toISOString()
        });
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('Error retrieving sample counts:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
});

// API endpoint to get all recordings metadata
app.get('/api/recordings', async (req, res) => {
  console.log('=== API: Get recordings metadata ===');

  try {
    // Initialize S3 client
    const s3Client = new S3Client({
      region: process.env.AWS_REGION || 'ap-southeast-2',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      }
    });

    const bucketName = process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting';

    // List all objects in the icu-videos/ prefix
    const listCommand = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: 'icu-videos/'
    });

    const response = await s3Client.send(listCommand);
    const recordings = [];
    let totalUploads = 0;
    let successfulUploads = 0;
    let lastUploadTime = null;

    if (response.Contents) {
      for (const object of response.Contents) {
        if (object.Key.endsWith('.mp4') || object.Key.endsWith('.webm')) {
          totalUploads++;

          try {
            // Get object metadata
            const headCommand = new HeadObjectCommand({
              Bucket: bucketName,
              Key: object.Key
            });

            const metadata = await s3Client.send(headCommand);

            // Parse S3 key to extract information
            // Format: icu-videos/[AGEGROUP]/[GENDER]/[ETHNICITY]/[PHRASE_LABEL]/[FILENAME]
            const keyParts = object.Key.split('/');
            if (keyParts.length >= 5) {
              const [, ageGroup, gender, ethnicity, phraseLabel] = keyParts;
              const filename = keyParts[keyParts.length - 1];

              recordings.push({
                id: object.Key,
                s3Key: object.Key,
                s3Url: `https://${bucketName}.s3.${process.env.AWS_REGION || 'ap-southeast-2'}.amazonaws.com/${object.Key}`,
                filename: filename,
                category: phraseLabel.replace(/_/g, ' '), // Convert underscores back to spaces
                phrase: metadata.Metadata?.phrase || phraseLabel.replace(/_/g, ' '),
                recordingNumber: parseInt(metadata.Metadata?.recordingnumber) || 1,
                demographics: {
                  userId: metadata.Metadata?.userid || 'unknown',
                  ageGroup: metadata.Metadata?.agegroup || ageGroup,
                  gender: metadata.Metadata?.gender || gender,
                  ethnicity: metadata.Metadata?.ethnicity || ethnicity
                },
                timestamp: metadata.Metadata?.timestamp || object.LastModified?.toISOString(),
                created: object.LastModified?.toISOString(),
                fileSize: object.Size,
                uploadTime: object.LastModified?.toISOString()
              });

              successfulUploads++;

              if (!lastUploadTime || object.LastModified > new Date(lastUploadTime)) {
                lastUploadTime = object.LastModified?.toISOString();
              }
            }
          } catch (metadataError) {
            console.warn(`Failed to get metadata for ${object.Key}:`, metadataError);
          }
        }
      }
    }

    console.log(`Found ${recordings.length} recordings in S3`);

    res.json({
      success: true,
      recordings: recordings,
      totalUploads: totalUploads,
      successfulUploads: successfulUploads,
      failedUploads: totalUploads - successfulUploads,
      lastUploadTime: lastUploadTime
    });

  } catch (error) {
    console.error('Error retrieving recordings:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error',
      recordings: [],
      totalUploads: 0,
      successfulUploads: 0,
      failedUploads: 0,
      lastUploadTime: null
    });
  }
});

// Start the server with explicit error handling
const server = app.listen(port, () => {
  console.log(`Server running on port ${port}`);
  console.log(`Upload endpoint: http://localhost:${port}/upload`);
  console.log(`Health check: http://localhost:${port}/health`);
  console.log(`Sample counts API: http://localhost:${port}/api/sample-counts`);
  console.log(`SageMaker metrics API: http://localhost:${port}/api/metrics`);
  console.log(`Recordings API: http://localhost:${port}/api/recordings`);
});

server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`Port ${port} is already in use. Please choose a different port.`);
  } else {
    console.error(`Error starting server: ${error.message}`);
  }
  process.exit(1);
});
